package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Todo represents a todo item
type Todo struct {
	ID       primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Text     string             `bson:"text" json:"text"`
	Completed  bool             `bson:"completed" json:"completed"`
	Priority int              `bson:"priority" json:"priority"`
	Repeat   string             `bson:"repeat" json:"repeat"`
	DueDate  string             `bson:"dueDate" json:"dueDate"`
	Details     string             `bson:"details" json:"details"`
	Tags        []string           `bson:"tags" json:"tags"`
	CreatedAt   string             `bson:"createdAt" json:"createdAt"`
	CompletedAt *string            `bson:"completedAt,omitempty" json:"completedAt,omitempty"` // Added completion timestamp field (pointer to allow null)
	CompletedDates []string         `bson:"completedDates,omitempty" json:"completedDates,omitempty"` // Added completion history
	IsArchived bool               `bson:"isArchived" json:"isArchived"` // Whether this todo has been archived to knowledge base
}

// Knowledge represents a knowledge base item
type Knowledge struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Title       string             `bson:"title" json:"title"`
	Content     string             `bson:"content" json:"content"`
	Tags        []string           `bson:"tags" json:"tags"`
	Priority    int                `bson:"priority" json:"priority"`
	CreatedAt   string             `bson:"createdAt" json:"createdAt"`
	ArchivedAt  string             `bson:"archivedAt" json:"archivedAt"`
	SourceTodoID primitive.ObjectID `bson:"sourceTodoId" json:"sourceTodoId"` // Reference to original todo
}

var todosCollection *mongo.Collection
var knowledgeCollection *mongo.Collection

func main() {
	// Load environment variables
	mongoURI := os.Getenv("MONGO_URI")
	if mongoURI == "" {
		mongoURI = "mongodb://localhost:27017" // Default MongoDB URI
	}
	dbName := os.Getenv("MONGO_DB_NAME")
	if dbName == "" {
		dbName = "todo" // Default database name
	}
	collectionName := os.Getenv("MONGO_COLLECTION_NAME")
	if collectionName == "" {
		collectionName = "todos" // Default collection name
	}

	// Initialize MongoDB connection
	clientOptions := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		log.Fatal(err)
	}

	err = client.Ping(context.TODO(), nil)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println("Connected to MongoDB!")

	todosCollection = client.Database(dbName).Collection(collectionName)
	knowledgeCollection = client.Database(dbName).Collection("knowledge")

	// Initialize Gin router
	router := gin.Default()

	// CORS middleware
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Define API endpoints
	router.GET("/todos", getTodosHandler)
	router.POST("/todos", createTodoHandler)
	router.PUT("/todos/:id", updateTodoHandler)
	router.DELETE("/todos/:id", deleteTodoHandler)

	// Knowledge base endpoints
	router.GET("/knowledge", getKnowledgeHandler)
	router.POST("/knowledge", createKnowledgeHandler)
	router.PUT("/knowledge/:id", updateKnowledgeHandler)
	router.DELETE("/knowledge/:id", deleteKnowledgeHandler)
	router.POST("/todos/:id/archive", archiveTodoHandler)

	// Start the server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8081" // Default port
	}
	router.Run(":" + port)
}

func getTodosHandler(c *gin.Context) {
	// Retrieve all todos from MongoDB
	filter := bson.D{} // Empty filter to retrieve all documents
	cursor, err := todosCollection.Find(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer cursor.Close(context.TODO())

	var todos []Todo
	if err := cursor.All(context.TODO(), &todos); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, todos)
}

func createTodoHandler(c *gin.Context) {
	// Parse request body
	var todo Todo
	if err := c.BindJSON(&todo); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Insert todo into MongoDB

	// Set CreatedAt to current time
	todo.CreatedAt = time.Now().Format(time.RFC3339)
	result, err := todosCollection.InsertOne(context.TODO(), todo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return the new todo with its ID
	insertedID := result.InsertedID.(primitive.ObjectID)
	todo.ID = insertedID
	c.JSON(http.StatusCreated, todo)
}

func updateTodoHandler(c *gin.Context) {
	// Get todo ID from URL
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// Parse request body
	var updatedTodo Todo
	if err := c.BindJSON(&updatedTodo); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Log received data for debugging
	log.Printf("Updating todo with data: %+v", updatedTodo)

	// Update todo in MongoDB
	filter := bson.M{"_id": objID}
	update := bson.M{"$set": updatedTodo}
	result, err := todosCollection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Log update result
	log.Printf("Update result: Matched %v, Modified %v", result.MatchedCount, result.ModifiedCount)

	// Return the updated todo
	updatedTodo.ID = objID
	c.JSON(http.StatusOK, updatedTodo)
}

func deleteTodoHandler(c *gin.Context) {
	// Get todo ID from URL
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// Delete todo from MongoDB
	filter := bson.M{"_id": objID}
	_, err = todosCollection.DeleteOne(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Knowledge base handlers
func getKnowledgeHandler(c *gin.Context) {
	// Retrieve all knowledge items from MongoDB
	filter := bson.D{} // Empty filter to retrieve all documents
	cursor, err := knowledgeCollection.Find(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer cursor.Close(context.TODO())

	var knowledge []Knowledge
	if err := cursor.All(context.TODO(), &knowledge); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, knowledge)
}

func createKnowledgeHandler(c *gin.Context) {
	// Parse request body
	var knowledge Knowledge
	if err := c.BindJSON(&knowledge); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set ArchivedAt to current time
	knowledge.ArchivedAt = time.Now().Format(time.RFC3339)
	result, err := knowledgeCollection.InsertOne(context.TODO(), knowledge)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return the new knowledge with its ID
	insertedID := result.InsertedID.(primitive.ObjectID)
	knowledge.ID = insertedID
	c.JSON(http.StatusCreated, knowledge)
}

func updateKnowledgeHandler(c *gin.Context) {
	// Get knowledge ID from URL
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// Parse request body
	var updatedKnowledge Knowledge
	if err := c.BindJSON(&updatedKnowledge); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update knowledge in MongoDB
	filter := bson.M{"_id": objID}
	update := bson.M{"$set": updatedKnowledge}
	result, err := knowledgeCollection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	log.Printf("Update result: Matched %v, Modified %v", result.MatchedCount, result.ModifiedCount)

	// Return the updated knowledge
	updatedKnowledge.ID = objID
	c.JSON(http.StatusOK, updatedKnowledge)
}

func deleteKnowledgeHandler(c *gin.Context) {
	// Get knowledge ID from URL
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// First, find the knowledge item to get the source todo ID
	var knowledge Knowledge
	filter := bson.M{"_id": objID}
	err = knowledgeCollection.FindOne(context.TODO(), filter).Decode(&knowledge)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Knowledge not found"})
		return
	}

	// Delete knowledge from MongoDB
	_, err = knowledgeCollection.DeleteOne(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Update the corresponding todo to unarchive it
	todoFilter := bson.M{"_id": knowledge.SourceTodoID}
	todoUpdate := bson.M{"$set": bson.M{"isArchived": false}}
	_, err = todosCollection.UpdateOne(context.TODO(), todoFilter, todoUpdate)
	if err != nil {
		log.Printf("Warning: Failed to unarchive todo %v: %v", knowledge.SourceTodoID, err)
		// Don't return error here as the knowledge was already deleted successfully
	}

	c.JSON(http.StatusNoContent, nil)
}

func archiveTodoHandler(c *gin.Context) {
	// Get todo ID from URL
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// Find the todo to archive
	var todo Todo
	filter := bson.M{"_id": objID}
	err = todosCollection.FindOne(context.TODO(), filter).Decode(&todo)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Todo not found"})
		return
	}

	// Check if todo is completed
	if !todo.Completed {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only completed todos can be archived"})
		return
	}

	// Create knowledge item from todo
	knowledge := Knowledge{
		Title:        todo.Text,
		Content:      todo.Details,
		Tags:         todo.Tags,
		Priority:     todo.Priority,
		CreatedAt:    todo.CreatedAt,
		ArchivedAt:   time.Now().Format(time.RFC3339),
		SourceTodoID: todo.ID,
	}

	// Insert knowledge into MongoDB
	result, err := knowledgeCollection.InsertOne(context.TODO(), knowledge)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Update todo to mark as archived
	update := bson.M{"$set": bson.M{"isArchived": true}}
	_, err = todosCollection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return the new knowledge with its ID
	insertedID := result.InsertedID.(primitive.ObjectID)
	knowledge.ID = insertedID
	c.JSON(http.StatusCreated, knowledge)
}