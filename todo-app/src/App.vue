<template>
  <el-container direction="horizontal">
    <el-aside width="200px">
      <el-menu
        background-color="#f0f0f0"
        text-color="#333"
        active-text-color="#409EFF"
        :default-active="activeFilterIndex"
        @select="handleFilterSelect"
      >
        <el-menu-item index="1">
          <el-icon>
          </el-icon>
          所有待办
        </el-menu-item>
        <el-menu-item index="2">
          <el-icon>
            <i class="el-icon-s-home"></i>
          </el-icon>
          未完成
        </el-menu-item>
        <el-menu-item index="3">
          <el-icon>
            <i class="el-icon-star-on"></i>
          </el-icon>
          重要
        </el-menu-item>
        <el-menu-item index="5">
          <el-icon>
            <i class="el-icon-s-flag"></i> <!-- Using a flag icon for 'Not Important' -->
          </el-icon>
          不重要
        </el-menu-item>
        <el-menu-item index="4">
          <el-icon>
            <i class="el-icon-check"></i>
          </el-icon>
          已完成
        </el-menu-item>
        <el-menu-item index="6">
          <el-icon>
            <i class="el-icon-collection"></i>
          </el-icon>
          知识库
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-container direction="vertical" style="flex-grow: 1;">
      <el-header
        style="
          text-align: right;
          font-size: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
        "
      >
        <div>{{ currentDateDisplay }}</div>
        <div>
          <el-button type="text">
            <el-icon>
              <i class="el-icon-setting"></i>
            </el-icon>
          </el-button>
          <el-avatar
            size="small"
            src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534cf638png.png"
          ></el-avatar>
        </div>
      </el-header>
      <el-main>
        <!-- Todo List View -->
        <div v-if="currentView === 'todos'">
          <el-input
            v-model="searchTerm"
            placeholder="按内容、优先级(P1-P5)或标签搜索..."
            clearable
            style="margin-bottom: 15px; padding: 0 20px;"
          ></el-input>
          <div class="todo-list-container">
            <div v-for="todo in sortedTodos" :key="todo.id" class="todo-item"
                 :class="{ 'selected-todo': selectedTodo && todo.id === selectedTodo.id }"
                 @click="selectTodo(todo)">
              <div :class="{ 'completed-todo': todo.completed }">
                <span style="margin-left: 10px;">{{ todo.text }}</span>
                <el-tag size="small" :type="getPriorityTagType(todo.priority)" class="priority-tag" style="margin-left: 10px;">P{{ todo.priority }}</el-tag>
                <el-tag v-if="todo.repeat !== '不重复'" size="small" type="info" class="repeat-tag" style="margin-left: 5px;">{{ todo.repeat }}</el-tag>
                <el-tag v-for="(tag, index) in todo.tags" :key="index" type="info" style="margin-left: 5px">{{ tag }}</el-tag>
                <el-tag size="small" type="info" style="margin-left: 5px;">创建于: {{ new Date(todo.createdAt).toLocaleDateString('zh-CN') }}</el-tag>
                <el-tag v-if="todo.completed && todo.completedAt" size="small" type="success" style="margin-left: 5px;">完成于: {{ new Date(todo.completedAt).toLocaleString('zh-CN') }}</el-tag>
              </div>
              <div class="todo-buttons">
                <el-button v-if="!todo.completed" type="success" size="small" @click.stop="markAsCompleted(todo)">完成</el-button>
                <el-button v-if="todo.completed" type="warning" size="small" @click.stop="redoTodo(todo)">重做</el-button>
                <el-button v-if="todo.completed && !todo.isArchived" type="primary" size="small" @click.stop="archiveTodo(todo)">沉淀</el-button>
                <el-button type="danger" size="small" @click.stop="deleteTodo(todo)">删除</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Knowledge Base View -->
        <div v-else-if="currentView === 'knowledge'">
          <el-input
            v-model="searchTerm"
            placeholder="搜索知识库..."
            clearable
            style="margin-bottom: 15px; padding: 0 20px;"
          ></el-input>
          <div class="knowledge-list-container">
            <div v-for="item in filteredKnowledge" :key="item.id" class="knowledge-item"
                 :class="{ 'selected-knowledge': selectedKnowledge && item.id === selectedKnowledge.id }"
                 @click="selectKnowledge(item)">
              <div class="knowledge-content">
                <h3 style="margin: 0 0 10px 10px;">{{ item.title }}</h3>
                <p style="margin: 0 0 10px 10px; color: #666;">{{ item.content.substring(0, 100) }}{{ item.content.length > 100 ? '...' : '' }}</p>
                <div style="margin-left: 10px;">
                  <el-tag size="small" :type="getPriorityTagType(item.priority)" style="margin-right: 5px;">P{{ item.priority }}</el-tag>
                  <el-tag v-for="(tag, index) in item.tags" :key="index" type="info" style="margin-right: 5px;">{{ tag }}</el-tag>
                  <el-tag size="small" type="info">沉淀于: {{ new Date(item.archivedAt).toLocaleDateString('zh-CN') }}</el-tag>
                </div>
              </div>
              <div class="knowledge-buttons">
                <el-button type="danger" size="small" @click.stop="deleteKnowledge(item)">删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-main>
      <div class="add-todo-container">
        <el-input v-model="newTodo" placeholder="添加新的待办事项" @keyup.enter="addTodo" class="add-todo-input-main"></el-input>
        <el-select v-model="newTodoPriority" placeholder="优先级" style="width: 100px; margin-left: 10px;">
          <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
        </el-select>
        <el-select v-model="newTodoRepeat" placeholder="重复" style="width: 120px; margin-left: 10px;">
          <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </el-container>
    <div class="todo-detail-container">
      <!-- Todo Detail View -->
      <div v-if="currentView === 'todos' && selectedTodo">
        <h2>{{ selectedTodo.text }}</h2>
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基础设置" name="1">
            <div class="detail-edit-item">
              <span>优先级: </span>
              <el-select v-model="selectedTodo.priority" @change="updateTodo" placeholder="优先级" style="width: 120px;">
                <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
              </el-select>
            </div>
            <div class="detail-edit-item">
              <span>重复: </span>
              <el-select v-model="selectedTodo.repeat" @change="updateTodo" placeholder="重复" style="width: 150px;">
                <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div class="detail-edit-item-date">
              <span class="detail-edit-item-date-span">截止日期: </span>
              <el-date-picker v-model="formattedDueDate" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 150px;display: inline-flex;"></el-date-picker> <!-- Use formattedDueDate here -->
              <!-- Removed @change="updateTodo" as it's handled in the computed setter -->
            </div>
            <div class="detail-edit-item">
              <span>标签: </span>
              <el-input v-model="newTag" @keyup.enter="addTag" placeholder="添加标签" style="width: 150px;"></el-input>
              <el-tag
                v-for="(tag, index) in selectedTodo.tags"
                :key="index"
                class="mx-1"
                type=""
                closable
                @close="removeTag(index)"
                style="margin-left: 5px;display: inline-flex;"
              >
                {{ tag }}
              </el-tag>
            </div>
          </el-collapse-item>
        </el-collapse>
        <div v-if="selectedTodo" class="detail-edit-item">
          <el-collapse v-model="showCompletionHistory">
            <el-collapse-item title="完成历史" name="1">
              <el-calendar v-model="calendarDate">
                <template #date-cell="{data}">
                  <div class="calendar-day">
                    {{ data.day.split('-').slice(2).join('-') }}
                    <div v-if="isDateCompleted(data.day)" class="completion-dot"></div>
                    <div v-if="isCreatedDate(data.day)" class="creation-dot"></div>
                  </div>
                </template>
              </el-calendar>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="detail-edit-item">
          <span>详情: </span>
          <el-input v-model="selectedTodo.details" @change="updateTodo" type="textarea" :rows="4" placeholder="输入详情"
            style="width: 100%; margin-top: 10px; height: 200px;" tabindex="0" @keydown="handleTabKey"></el-input>
        </div>
      </div>

      <!-- Knowledge Detail View -->
      <div v-else-if="currentView === 'knowledge' && selectedKnowledge">
        <h2>{{ selectedKnowledge.title }}</h2>
        <div class="detail-edit-item">
          <span>优先级: </span>
          <el-tag :type="getPriorityTagType(selectedKnowledge.priority)">P{{ selectedKnowledge.priority }}</el-tag>
        </div>
        <div class="detail-edit-item">
          <span>标签: </span>
          <el-tag v-for="(tag, index) in selectedKnowledge.tags" :key="index" type="info" style="margin-right: 5px;">{{ tag }}</el-tag>
        </div>
        <div class="detail-edit-item">
          <span>沉淀时间: </span>
          <span>{{ new Date(selectedKnowledge.archivedAt).toLocaleString('zh-CN') }}</span>
        </div>
        <div class="detail-edit-item">
          <span>内容: </span>
          <div style="margin-top: 10px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9; white-space: pre-wrap;">{{ selectedKnowledge.content }}</div>
        </div>
      </div>

      <div v-else>
        <p v-if="currentView === 'todos'">请选择一个待办事项查看详情</p>
        <p v-else-if="currentView === 'knowledge'">请选择一个知识库项目查看详情</p>
      </div>
    </div>
  </el-container>
</template>

<script>
import { defineComponent, ref, onMounted, computed, nextTick } from 'vue'
import { ElDatePicker, ElCalendar } from 'element-plus'
import axios from 'axios'

export default defineComponent({
  name: 'App',
  setup() {
    const newTodo = ref('')
    const newTodoPriority = ref(1)
    const newTodoRepeat = ref('不重复')
    const selectedTodo = ref({ // To store the selected todo item
      tags: []
    })
    const selectedKnowledge = ref(null) // To store the selected knowledge item
    const newTag = ref('')
    const activeFilterIndex = ref('2') // Default to '所有待办'
    const searchTerm = ref('') // Add ref for the search term

    const repeatOptions = ref([
      { value: '不重复', label: '不重复' },
      { value: '工作日重复', label: '工作日重复' },
      { value: '每日重复', label: '每日重复' },
      { value: '周末重复', label: '周末重复' },
    ])
    const todos = ref([])
    const knowledge = ref([])
    const currentView = ref('todos') // 'todos' or 'knowledge'

    const apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8081';

    const isWeekday = (date) => {
      const day = date.getDay();
      return day >= 1 && day <= 5; // 1-5是周一到周五
    }

    const checkRecurringTodos = () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 设置为今天的开始时间

      todos.value.forEach(todo => {
        if (todo.completed && todo.completedAt) {
          const completedDate = new Date(todo.completedAt);
          completedDate.setHours(0, 0, 0, 0); // 设置为完成日期的开始时间

          // 检查是否需要重置为未完成
          let shouldReset = false;

          if (todo.repeat === '每日重复' && completedDate < today) {
            shouldReset = true;
          } else if (todo.repeat === '工作日重复' && completedDate < today && isWeekday(today)) {
            shouldReset = true;
          } else if (todo.repeat === '周末重复' && completedDate < today && !isWeekday(today)) {
            shouldReset = true;
          }

          if (shouldReset) {
            axios.put(`${apiBaseUrl}/todos/${todo.id}`, {
              ...todo,
              completed: false,
              completedAt: null
            })
            .then(() => {
              // 确保触发响应式更新
              const index = todos.value.findIndex(t => t.id === todo.id);
              if (index !== -1) {
                todos.value[index] = {
                  ...todos.value[index],
                  completed: false,
                  completedAt: null
                };
                // 强制触发响应式更新
                todos.value = [...todos.value];
              }
            })
            .catch(error => {
              console.error('Error resetting recurring todo:', error);
            });
          }
        }
      });
    }

    onMounted(() => {
      // Load todos
      axios.get(`${apiBaseUrl}/todos`)
        .then(async response => { // Make the callback async to use await for updates
          const fetchedTodos = response.data || [];
          const today = new Date();
          const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

          const updatePromises = []; // Collect update promises

          fetchedTodos.forEach(todo => {
            if (todo.dueDate && todo.priority < 4) {
              let todoDueDateString = null;
              try {
                // Attempt to parse dueDate, assuming it might be ISO or YYYY-MM-DD
                const dueDate = new Date(todo.dueDate);
                 if (!isNaN(dueDate.getTime())) {
                    todoDueDateString = `${dueDate.getFullYear()}-${(dueDate.getMonth() + 1).toString().padStart(2, '0')}-${dueDate.getDate().toString().padStart(2, '0')}`;
                 } else if (typeof todo.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(todo.dueDate)) {
                    // Handle case where it's already YYYY-MM-DD string
                    todoDueDateString = todo.dueDate;
                 }
              } catch (e) {
                 console.error("Error parsing dueDate for priority check:", todo.dueDate, e);
              }

              if (todoDueDateString === todayString) {
                console.log(`Todo ID ${todo.id} due today with priority ${todo.priority}, updating to P4.`);
                todo.priority = 4; // Update priority locally first
                // Add the update request to the promises array
                updatePromises.push(
                  axios.put(`${apiBaseUrl}/todos/${todo.id}`, { ...todo, priority: 4 })
                    .catch(err => {
                      console.error(`Failed to update priority for todo ${todo.id}:`, err);
                      // Optionally revert local change or handle error
                    })
                );
              }
            }
          });

          // Wait for all potential updates to complete
          await Promise.all(updatePromises);

          // Assign the potentially modified list to the ref
          todos.value = fetchedTodos;
          checkRecurringTodos(); // 检查重复todo
        })
        .catch(error => {
          console.error('Error fetching todos:', error);
        });

      // Load knowledge base
      axios.get(`${apiBaseUrl}/knowledge`)
        .then(response => {
          knowledge.value = response.data || [];
        })
        .catch(error => {
          console.error('Error fetching knowledge:', error);
        });
    })

    const formattedDueDate = computed({
      get() {
        if (!selectedTodo.value || !selectedTodo.value.dueDate) {
          return null;
        }
        // Assuming dueDate might be a full ISO string or Date object
        try {
          const date = new Date(selectedTodo.value.dueDate);
          // Check if date is valid
          if (isNaN(date.getTime())) {
            // Handle potentially invalid date string from backend or initial state
            // Let's try to see if it's already YYYY-MM-DD
            if (typeof selectedTodo.value.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(selectedTodo.value.dueDate)) {
               return selectedTodo.value.dueDate;
            }
            console.warn("Invalid date value for dueDate:", selectedTodo.value.dueDate);
            return null;
          }
          // Format to YYYY-MM-DD
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          return `${year}-${month}-${day}`;
        } catch (e) {
          console.error("Error parsing dueDate:", selectedTodo.value.dueDate, e);
          return null;
        }
      },
      set(newValue) {
        if (selectedTodo.value) {
          // newValue from el-date-picker with value-format="YYYY-MM-DD" should already be in the correct format or null
          selectedTodo.value.dueDate = newValue;
          // The @change handler on the component calls updateTodo, so no need to call it here explicitly
          updateTodo(); // Call updateTodo when the formatted date changes
        }
      }
    });

    const addTodo = () => {
      if (newTodo.value.trim() !== '') {
        axios.post(`${apiBaseUrl}/todos`, {
          text: newTodo.value,
          completed: false,
          priority: newTodoPriority.value,
          repeat: newTodoRepeat.value,
          dueDate: null,
          details: '',
          tags: [],
          createdAt: new Date().toISOString(),
        })
          .then(response => {
            if (!Array.isArray(todos.value)) {
              todos.value = []
            }
            todos.value.push(response.data)
            newTodo.value = ''
            newTag.value = ''
          })
          .catch(error => {
            console.error('Error adding todo:', error)
            // 确保todos.value保持为数组
            if (!Array.isArray(todos.value)) {
              todos.value = []
            }
          })
      }
    }

    const selectTodo = (todo) => {
      selectedTodo.value = todo
      newTag.value = ''
    }

    const markAsCompleted = (todoToComplete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToComplete.id)
      if (index !== -1) {
        const completedAt = new Date().toISOString();
        const completedDates = todoToComplete.completedDates || [];
        completedDates.push(completedAt);
        axios.put(`${apiBaseUrl}/todos/${todoToComplete.id}`, {
          ...(todoToComplete || {}),
          completed: true,
          completedAt: completedAt,
          completedDates: completedDates
        })
          .then(() => {
            todos.value[index].completed = true;
            todos.value[index].completedAt = completedAt;
            todos.value[index].completedDates = completedDates;
            // Trigger reactivity for sorting
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error marking todo as completed:', error)
          })
      }
    }

    const redoTodo = (todoToRedo) => {
      const index = todos.value.findIndex(todo => todo.id === todoToRedo.id)
      if (index !== -1) {
        const today = new Date().toISOString().split('T')[0];
        const updatedDates = (todoToRedo.completedDates || [])
          .filter(date => !date.startsWith(today));

        // Create updated todo object with all fields
        const updatedTodo = {
          ...todoToRedo,
          completed: false,
          completedAt: null,
          completedDates: updatedDates
        };

        console.log('Sending update:', updatedTodo);

        axios.put(`${apiBaseUrl}/todos/${todoToRedo.id}`, updatedTodo)
          .then((response) => {
            console.log('Update successful:', response.data);
            todos.value[index] = {
              ...response.data,
              completedDates: updatedDates
            };
            // Trigger reactivity for sorting
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error redoing todo:', error.response?.data || error.message)
          })
      }
    }

    const updateTodo = () => {
      const index = todos.value.findIndex(todo => todo.id === selectedTodo.value.id)
      if (index !== -1) {
        axios.put(`${apiBaseUrl}/todos/${selectedTodo.value.id}`, selectedTodo.value || {})
          .then(() => {
            todos.value[index] = {
              ...selectedTodo.value
            }
            // Trigger reactivity
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error updating todo:', error)
          })
      }
    }

    const addTag = () => {
      if (newTag.value.trim() !== '') {
        if (!selectedTodo.value) return;
        if (!Array.isArray(selectedTodo.value.tags)) {
          selectedTodo.value.tags = [];
        }
        axios.put(`${apiBaseUrl}/todos/${selectedTodo.value.id}`, {
            ...(selectedTodo.value || {}),
            tags: [...selectedTodo.value.tags, newTag.value.trim()]
          })
            .then(() => {
              selectedTodo.value.tags.push(newTag.value.trim())
              newTag.value = ''
              updateTodo()
            })
            .catch(error => {
              console.error('Error adding tag:', error)
              if (!Array.isArray(selectedTodo.value.tags)) {
                selectedTodo.value.tags = []
              }
            })
      }
    }

    const deleteTodo = (todoToDelete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToDelete.id)
      if (index !== -1) {
        axios.delete(`${apiBaseUrl}/todos/${todoToDelete.id}`)
          .then(() => {
            todos.value.splice(index, 1)
            if (selectedTodo.value && selectedTodo.value.id === todoToDelete.id) {
              selectedTodo.value = null
            }
          })
          .catch(error => {
            console.error('Error deleting todo:', error)
          })
      }
    }

    const removeTag = (index) => {
        if (!selectedTodo.value) return;
        if (!Array.isArray(selectedTodo.value.tags)) {
          selectedTodo.value.tags = [];
          return;
        }
        if (index === -1 || index >= selectedTodo.value.tags.length) return;

                axios.put(`${apiBaseUrl}/todos/${selectedTodo.value.id}`, {
            ...(selectedTodo.value || {}),
            tags: selectedTodo.value.tags.filter((_, i) => i !== index)
          })
            .then(() => {
              selectedTodo.value.tags.splice(index, 1);
              // Trigger reactivity manually
              selectedTodo.value = { ...selectedTodo.value };
              updateTodo();
            })
            .catch(error => {
              console.error('Error removing tag:', error);
              if (!Array.isArray(selectedTodo.value.tags)) {
                selectedTodo.value.tags = []
              }
            });
    }

    const handleFilterSelect = (index) => {
      activeFilterIndex.value = index
      if (index === '6') {
        currentView.value = 'knowledge'
        selectedTodo.value = null
      } else {
        currentView.value = 'todos'
        selectedKnowledge.value = null
      }
    }

    const getPriorityTagType = (priority) => {
      switch (priority) {
        case 5: return 'danger';  // P5 - Red
        case 4: return 'warning'; // P4 - Orange
        case 3: return 'primary'; // P3 - Blue (Default Theme Color)
        case 2: return 'success'; // P2 - Green
        case 1: return 'info';    // P1 - Gray
        default: return '';       // Default/Fallback
      }
    }

    const calendarDate = ref(new Date());
    const showCompletionHistory = ref(false); // 控制完成历史显示
    const activeNames = ref(['1']); // 控制基础设置折叠面板，默认展开

    const isDateCompleted = (dateString) => {
      if (!selectedTodo.value?.completedDates?.length) return false;
      const date = new Date(dateString);
      return selectedTodo.value.completedDates.some(completedAt => {
        const completedDate = new Date(completedAt);
        return (
          date.getFullYear() === completedDate.getFullYear() &&
          date.getMonth() === completedDate.getMonth() &&
          date.getDate() === completedDate.getDate()
        );
      });
    };

    const isCreatedDate = (dateString) => {
      if (!selectedTodo.value?.createdAt) return false;
      const date = new Date(dateString);
      const createdDate = new Date(selectedTodo.value.createdAt);
      return (
        date.getFullYear() === createdDate.getFullYear() &&
        date.getMonth() === createdDate.getMonth() &&
        date.getDate() === createdDate.getDate()
      );
    };

    const currentDateDisplay = computed(() => {
      const now = new Date();
      const options = { month: 'long', day: 'numeric', weekday: 'long' };
      return now.toLocaleDateString('zh-CN', options);
    });

    const handleTabKey = (event) => {
      if (event.key === 'Tab') {
        event.preventDefault();
        const start = event.target.selectionStart;
        const end = event.target.selectionEnd;
        const value = selectedTodo.value.details || '';
        selectedTodo.value.details = value.substring(0, start) + '\t' + value.substring(end);
        // 移动光标到插入位置后
        nextTick(() => {
          event.target.selectionStart = event.target.selectionEnd = start + 1;
        });
      }
    };

    // Knowledge base functions
    const selectKnowledge = (knowledgeItem) => {
      selectedKnowledge.value = knowledgeItem
    }

    const archiveTodo = (todo) => {
      axios.post(`${apiBaseUrl}/todos/${todo.id}/archive`)
        .then(response => {
          // Add to knowledge base
          knowledge.value.push(response.data)
          // Update todo to mark as archived
          const index = todos.value.findIndex(t => t.id === todo.id)
          if (index !== -1) {
            todos.value[index].isArchived = true
            todos.value = [...todos.value]
          }
        })
        .catch(error => {
          console.error('Error archiving todo:', error)
        })
    }

    const deleteKnowledge = (knowledgeItem) => {
      const index = knowledge.value.findIndex(item => item.id === knowledgeItem.id)
      if (index !== -1) {
        axios.delete(`${apiBaseUrl}/knowledge/${knowledgeItem.id}`)
          .then(() => {
            // Remove from knowledge list
            knowledge.value.splice(index, 1)
            if (selectedKnowledge.value && selectedKnowledge.value.id === knowledgeItem.id) {
              selectedKnowledge.value = null
            }

            // Update the corresponding todo to unarchive it
            const todoIndex = todos.value.findIndex(todo => todo.id === knowledgeItem.sourceTodoId)
            if (todoIndex !== -1) {
              todos.value[todoIndex].isArchived = false
              // Trigger reactivity
              todos.value = [...todos.value]
            }
          })
          .catch(error => {
            console.error('Error deleting knowledge:', error)
          })
      }
    }

    const filteredKnowledge = computed(() => {
      if (!Array.isArray(knowledge.value)) return [];

      let filtered = [...knowledge.value];
      const currentSearchTerm = searchTerm.value.trim().toLowerCase();

      if (currentSearchTerm) {
        filtered = filtered.filter(item => {
          const searchTitle = item.title.toLowerCase();
          const searchContent = item.content.toLowerCase();
          const searchTags = item.tags ? item.tags.map(tag => tag.toLowerCase()) : [];

          return (
            searchTitle.includes(currentSearchTerm) ||
            searchContent.includes(currentSearchTerm) ||
            searchTags.some(tag => tag.includes(currentSearchTerm))
          );
        });
      }

      // Sort by archived date descending (newest first)
      return filtered.sort((a, b) => {
        const dateA = new Date(a.archivedAt).getTime();
        const dateB = new Date(b.archivedAt).getTime();
        return dateB - dateA;
      });
    });

    return {
      // 注册组件
      ElDatePicker,
      ElCalendar,
      newTodo,
      newTodoPriority,
      newTodoRepeat,
      repeatOptions,
      todos,
      selectedTodo, // Expose selectedTodo
      addTodo,
      selectTodo, // Expose selectTodo
      markAsCompleted, // Expose markAsCompleted
      redoTodo, // Expose redoTodo
      sortedTodos: computed(() => {
        if (!Array.isArray(todos.value)) return [];

        let filtered = [...todos.value];
        const currentSearchTerm = searchTerm.value.trim().toLowerCase();

        // 1. Apply left menu filter
        if (activeFilterIndex.value === '3') { // 重要 (P4+)
          filtered = filtered.filter(todo => todo.priority >= 4 && !todo.completed);
        } else if (activeFilterIndex.value === '5') { // 不重要 (P3-)
          filtered = filtered.filter(todo => todo.priority <= 3 && !todo.completed);
        } else if (activeFilterIndex.value === '4') { // 已完成 (All completed, regardless of date)
          filtered = filtered.filter(todo => todo.completed);
        } else if (activeFilterIndex.value === '2') { // 所有待办 (Uncompleted + Completed Today)
           filtered = filtered.filter(todo =>
             !todo.completed
           );
        }
        // else: index '1' is the sidebar search, not the main content search.

        // 2. Apply search term filter (if searchTerm is not empty)
        if (currentSearchTerm) {
          filtered = filtered.filter(todo => {
            const searchText = todo.text.toLowerCase();
            const searchPriority = `p${todo.priority}`;
            const searchTags = todo.tags ? todo.tags.map(tag => tag.toLowerCase()) : []; // Handle case where tags might be null/undefined initially

            return (
              searchText.includes(currentSearchTerm) ||
              searchPriority === currentSearchTerm || // Exact match for priority like "p3"
              searchTags.some(tag => tag.includes(currentSearchTerm)) // Check if any tag includes the search term
            );
          });
        }

        // 3. Sort the filtered list
        return filtered.sort((a, b) => {
          if (activeFilterIndex.value === '4') {
            // Sort completed items by completedAt descending (newest first)
            const dateA = a.completedAt ? new Date(a.completedAt).getTime() : 0;
            const dateB = b.completedAt ? new Date(b.completedAt).getTime() : 0;
            return dateB - dateA;
          } else {
            // Default sort: uncompleted first, then by priority descending
            if (a.completed !== b.completed) {
              return a.completed ? 1 : -1; // Uncompleted items come first
            }
            return b.priority - a.priority; // Higher priority first for items with same completion status
          }
        });
      }), // Expose sortedTodos
      updateTodo, // Expose updateTodo,
      newTag,
      searchTerm, // Expose searchTerm
      currentDateDisplay, // Expose computed date display
      activeFilterIndex, // Expose activeFilterIndex
      handleFilterSelect, // Expose handleFilterSelect
      getPriorityTagType, // Expose the new method
      addTag,
      removeTag, // Expose removeTag
      deleteTodo, // Expose deleteTodo
      calendarDate,
      isDateCompleted,
      isCreatedDate,
      showCompletionHistory,
      activeNames,
      formattedDueDate, // Expose formattedDueDate
      handleTabKey, // Expose handleTabKey
      // Knowledge base related
      knowledge,
      currentView,
      selectedKnowledge,
      selectKnowledge,
      archiveTodo,
      deleteKnowledge,
      filteredKnowledge
    }
  },
  methods: {
  }
})
</script>

<style>
body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", Arial, sans-serif;
}

.el-container {
  height: 100vh;
}

.el-header {
  background-color: #f5f7fa;
  color: #999;
  text-align: right;
  line-height: 60px;
  border-bottom: 1px solid #ccc;
  padding: 0 20px;
}

.el-aside {
  width: 250px !important;
  background-color: #f0f2f5;
  color: #333;
}

.el-main {
  background-color: #fff;
  color: #333;
  text-align: left;
  /* padding: 20px; */
  /* Remove padding if using flex wrapper */
}
.completed-todo span {
  text-decoration: line-through;
}

.selected-todo {
  background-color: #f5f7fa;
  border-left: 3px solid #409EFF;
}
/* Remove footer styles if footer is removed */
.el-footer {
  padding: 20px;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999;
}

body>.el-container {
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999;
}

body>.el-container {
  margin-bottom: 40px;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
  line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
  line-height: 320px;
}

.el-menu {
  border-right: none;
  background-color: transparent;
}

.el-menu-item {
  text-align: left;
  font-size: 14px;
  color: #999;
}

.el-menu-item i {
  color: #999;
}

.el-menu-item.is-active {
  color: #409EFF;
}

.el-menu-item i {
  color: #409EFF;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.todo-checkbox {
  font-size: 16px;
  color: #666;
}

.todo-checkbox .el-checkbox__inner {
  width: 20px;
  height: 20px;
  background-color: #fff;
  border-color: #ccc;
}

.todo-checkbox .el-checkbox__inner::after {
  width: 5px;
  height: 10px;
  left: 6px;
  top: 1px;
  border-color: #666;
}

.el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #fff;
  border: none;
}

.add-todo-container {
  display: flex;
  padding: 20px;
  border-top: 1px solid #eee;
}

.add-todo-input-main {
  flex-grow: 1;
}

.priority-tag {
  /* Style el-main directly */
  padding: 20px;
  flex-grow: 1;
  overflow-y: auto;
  box-sizing: border-box;
}

.todo-list-container {
  width: 95%; /* 调整宽度 */
  padding: 20px;
  /* border-right: 1px solid #eee; */ /* 移除竖线 */
  overflow-y: auto;
}

.add-todo-container {
  display: flex;
  padding: 20px;
  border-top: 1px solid #eee;
}

.todo-detail-container {
  width: 40%;
  padding: 20px;
  border-left: 1px solid #eee;
  overflow-y: auto;
  background-color: #fafafa;
}
.detail-edit-item-date{
  margin-bottom: 15px;
}
.detail-edit-item-date-span {
  width: 80px;
  display: inline-block;
  color: #666;
}
.detail-edit-item {
  margin-bottom: 15px;
}

.detail-edit-item span {
  width: 80px;
  display: inline-block;
  color: #666;
}

.todo-buttons {
  display: flex;
  align-items: center;
}

.todo-buttons .el-button {
  margin-left: 5px; /* 调整按钮之间的间距 */
}

.calendar-day {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.completion-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67C23A;
  margin-top: 2px;
}

.creation-dot {
  background-color: rgba(64, 158, 255, 0.15);
  border-radius: 4px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.el-calendar {
  margin-top: 10px;
}

.el-calendar__body {
  padding: 0;
}

/* Knowledge base styles */
.knowledge-list-container {
  width: 95%;
  padding: 20px;
  overflow-y: auto;
}

.knowledge-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.knowledge-item:hover {
  background-color: #f5f7fa;
}

.selected-knowledge {
  background-color: #f5f7fa;
  border-left: 3px solid #409EFF;
}

.knowledge-content {
  flex-grow: 1;
}

.knowledge-content h3 {
  margin: 0 0 10px 10px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.knowledge-content p {
  margin: 0 0 10px 10px;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.knowledge-buttons {
  display: flex;
  align-items: center;
}

.knowledge-buttons .el-button {
  margin-left: 5px;
}
</style>
